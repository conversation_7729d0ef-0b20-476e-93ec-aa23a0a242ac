#include "my_config.h"

extern uint16_t adc_value[1];
extern sampling_state_t sampling_state;
extern config_params_t system_config;

// 采样时间管理变量
uint32_t last_tick_sampling;
static uint32_t next_sample_time = 0;       // 预计算的下次采样时间

// ADC性能统计变量
static uint32_t adc_sample_count = 0;       // ADC采样次数统计
static uint16_t last_adc_value = ADC_INVALID_VALUE;  // 上次ADC值，用于数据有效性检查

void sampling_task(void);

/**
 * @brief 检查ADC数据是否准备就绪
 * @details 验证ADC数据的有效性，包括数值范围检查和数据变化检测
 * @return 1=数据准备就绪，0=数据未准备好
 */
uint8_t adc_is_data_ready(void)
{
    // 安全检查：验证ADC值是否在有效范围内
    if (adc_value[0] > (uint16_t)ADC_MAX_VALUE) {
        REPORT_WARNING(ERR_ADC_DATA_INVALID, "adc_app", "ADC value exceeds maximum range");
        return 0;
    }

    // 检查ADC值是否有效（非全0或全1）
    if (adc_value[0] == 0 || adc_value[0] == 0xFFFF) {
        return 0;
    }

    // 检查ADC值是否发生变化（避免读取过时数据）
    if (adc_value[0] == last_adc_value) {
        return 0;
    }

    return 1;
}

static inline float calculate_voltage(uint16_t adc_raw)
{
    // 安全检查：验证ADC原始值范围
    if (adc_raw > (uint16_t)ADC_MAX_VALUE) {
        REPORT_WARNING(ERR_ADC_DATA_INVALID, "adc_app", "ADC raw value out of range");
        return 0.0f;
    }

    // 安全检查：验证配置参数有效性
    if (system_config.ratio_ch0 <= MIN_CONFIG_RATIO || system_config.ratio_ch0 > MAX_CONFIG_RATIO) {
        REPORT_WARNING(ERR_CONFIG_INVALID, "adc_app", "Invalid ratio configuration");
        return 0.0f;
    }

    // 使用常量定义，减少魔数
    float voltage_raw = (adc_raw * ADC_REFERENCE_VOLTAGE) / ADC_MAX_VALUE;
    return voltage_raw * system_config.ratio_ch0;
}

void adc_task(void)
{
    sampling_task();
}

void sampling_task(void)
{
    if (!sampling_state.is_sampling) {
        return;
    }

    uint32_t current_time = get_system_ms();

    // 优化时间比较：使用预计算的下次采样时间
    if (current_time >= next_sample_time) {
        // 预计算下次采样时间，避免重复计算
        next_sample_time = current_time + (sampling_state.sample_period * SAMPLE_PERIOD_TO_MS);
        last_tick_sampling = current_time;

        // 数据有效性检查：确保ADC数据准备就绪
        if (!adc_is_data_ready()) {
            return;  // ADC数据未准备好，跳过本次采样
        }

        // 更新ADC值缓存，用于下次有效性检查
        last_adc_value = adc_value[0];

        // 性能统计：记录采样次数
        adc_sample_count++;

        // 优化的电压计算：使用内联函数和常量定义
        float voltage = calculate_voltage(adc_value[0]);

        // 优化阈值判断：直接使用配置值，无需volatile
        uint8_t over_limit = (voltage > system_config.limit_ch0) ? 1 : 0;

        // 打印采样数据
        print_sample_data(voltage, over_limit);

        // 通知OLED更新电压显示（与采样数据同步）
        oled_update_voltage(voltage, get_unix_timestamp());

        // 控制LED2（超限指示）- 使用统一的LED控制接口
        led2_overlimit_set(over_limit);
    }
}

uint32_t adc_get_sample_count(void)
{
    return adc_sample_count;
}

/**
 * @brief 重置ADC性能统计
 */
void adc_reset_stats(void)
{
    adc_sample_count = 0;
    last_adc_value = ADC_INVALID_VALUE;
}

float adc_get_current_voltage(void)
{
    // 检查ADC数据是否准备就绪
    if (!adc_is_data_ready()) {
        return 0.0f;  // 数据未准备好，返回0
    }

    // 使用现有的电压计算逻辑
    return calculate_voltage(adc_value[0]);
}
