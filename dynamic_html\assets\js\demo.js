// 系统演示功能JavaScript文件

// 演示系统状态
let demoState = {
    isRunning: false,
    samplePeriod: 5,
    adcRatio: 1.0,
    currentVoltage: 0.0,
    sampleCount: 0,
    voltageHistory: [],
    maxHistoryLength: 50,
    chart: null,
    animationId: null
};

// 系统配置
const systemConfig = {
    adcMaxValue: 4095,
    referenceVoltage: 3.3,
    voltageLimit: 3.0,
    updateInterval: 100, // ms
    chartUpdateInterval: 1000 // ms
};

// DOM加载完成后初始化演示
document.addEventListener('DOMContentLoaded', function() {
    initializeDemo();
});

// 初始化演示系统
function initializeDemo() {
    initializeDemoControls();
    initializeVirtualDisplay();
    initializeLEDPanel();
    initializeVoltageChart();
    
    console.log('系统演示模块已初始化');
}

// 初始化演示控制面板
function initializeDemoControls() {
    const startBtn = document.getElementById('startSampling');
    const stopBtn = document.getElementById('stopSampling');
    const periodSlider = document.getElementById('samplePeriod');
    const ratioSlider = document.getElementById('adcRatio');
    const periodValue = document.getElementById('periodValue');
    const ratioValue = document.getElementById('ratioValue');

    // 开始采样按钮
    if (startBtn) {
        startBtn.addEventListener('click', startSampling);
    }

    // 停止采样按钮
    if (stopBtn) {
        stopBtn.addEventListener('click', stopSampling);
    }

    // 采样周期滑块
    if (periodSlider && periodValue) {
        periodSlider.addEventListener('input', function() {
            demoState.samplePeriod = parseInt(this.value);
            periodValue.textContent = this.value;
        });
    }

    // ADC变比滑块
    if (ratioSlider && ratioValue) {
        ratioSlider.addEventListener('input', function() {
            demoState.adcRatio = parseFloat(this.value);
            ratioValue.textContent = this.value;
        });
    }
}

// 开始采样
function startSampling() {
    if (demoState.isRunning) return;

    demoState.isRunning = true;
    demoState.sampleCount = 0;
    demoState.voltageHistory = [];

    // 更新按钮状态
    const startBtn = document.getElementById('startSampling');
    const stopBtn = document.getElementById('stopSampling');
    
    if (startBtn) {
        startBtn.disabled = true;
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 采样中...';
    }
    
    if (stopBtn) {
        stopBtn.disabled = false;
    }

    // 更新OLED显示
    updateOLEDDisplay('采样模式', '正在初始化...');

    // 启动LED1闪烁（采样状态指示）
    startLEDBlink('led1');

    // 开始采样循环
    startSamplingLoop();

    console.log('开始采样，周期:', demoState.samplePeriod, '秒');
}

// 停止采样
function stopSampling() {
    if (!demoState.isRunning) return;

    demoState.isRunning = false;

    // 更新按钮状态
    const startBtn = document.getElementById('startSampling');
    const stopBtn = document.getElementById('stopSampling');
    
    if (startBtn) {
        startBtn.disabled = false;
        startBtn.innerHTML = '<i class="fas fa-play"></i> 开始采样';
    }
    
    if (stopBtn) {
        stopBtn.disabled = true;
        stopBtn.innerHTML = '<i class="fas fa-stop"></i> 停止采样';
    }

    // 更新OLED显示
    updateOLEDDisplay('system idle', '');

    // 停止所有LED闪烁
    stopAllLEDBlink();

    // 停止采样循环
    if (demoState.animationId) {
        cancelAnimationFrame(demoState.animationId);
        demoState.animationId = null;
    }

    console.log('停止采样，总采样次数:', demoState.sampleCount);
}

// 采样循环
function startSamplingLoop() {
    let lastSampleTime = Date.now();
    let lastChartUpdate = Date.now();

    function sampleLoop() {
        if (!demoState.isRunning) return;

        const currentTime = Date.now();

        // 检查是否需要采样
        if (currentTime - lastSampleTime >= demoState.samplePeriod * 1000) {
            performSample();
            lastSampleTime = currentTime;
        }

        // 检查是否需要更新图表
        if (currentTime - lastChartUpdate >= systemConfig.chartUpdateInterval) {
            updateVoltageChart();
            lastChartUpdate = currentTime;
        }

        // 持续更新显示
        updateRealTimeDisplay();

        demoState.animationId = requestAnimationFrame(sampleLoop);
    }

    sampleLoop();
}

// 执行一次采样
function performSample() {
    // 模拟ADC采样（生成随机电压值）
    const baseVoltage = 2.5; // 基础电压
    const noise = (Math.random() - 0.5) * 0.5; // ±0.25V噪声
    const rawVoltage = Math.max(0, Math.min(systemConfig.referenceVoltage, baseVoltage + noise));
    
    // 应用变比系数
    demoState.currentVoltage = rawVoltage * demoState.adcRatio;
    
    // 增加采样计数
    demoState.sampleCount++;

    // 添加到历史记录
    const timestamp = new Date();
    demoState.voltageHistory.push({
        time: timestamp,
        voltage: demoState.currentVoltage,
        sample: demoState.sampleCount
    });

    // 限制历史记录长度
    if (demoState.voltageHistory.length > demoState.maxHistoryLength) {
        demoState.voltageHistory.shift();
    }

    // 检查超限
    const isOverLimit = demoState.currentVoltage > systemConfig.voltageLimit;
    
    if (isOverLimit) {
        // 启动LED2（超限报警）
        setLEDState('led2', true);
        console.log('电压超限:', demoState.currentVoltage.toFixed(2), 'V');
    } else {
        setLEDState('led2', false);
    }

    // 更新OLED显示
    const timeStr = timestamp.toLocaleTimeString();
    const voltageStr = demoState.currentVoltage.toFixed(2) + 'V';
    updateOLEDDisplay(timeStr, voltageStr);

    console.log('采样 #' + demoState.sampleCount + ':', voltageStr, isOverLimit ? '(超限)' : '');
}

// 更新实时显示
function updateRealTimeDisplay() {
    // 这里可以添加其他实时更新的内容
    // 例如系统状态LED的随机闪烁等
    
    if (demoState.isRunning && Math.random() < 0.01) {
        // 随机闪烁系统状态LED
        const systemLEDs = ['led3', 'led4', 'led5', 'led6'];
        const randomLED = systemLEDs[Math.floor(Math.random() * systemLEDs.length)];
        flashLED(randomLED, 200);
    }
}

// 初始化虚拟OLED显示
function initializeVirtualDisplay() {
    updateOLEDDisplay('system idle', '');
}

// 更新OLED显示
function updateOLEDDisplay(line1, line2) {
    const oledLine1 = document.getElementById('oledLine1');
    const oledLine2 = document.getElementById('oledLine2');

    if (oledLine1) {
        oledLine1.textContent = line1;
        oledLine1.classList.add('fade-in');
        setTimeout(() => oledLine1.classList.remove('fade-in'), 300);
    }

    if (oledLine2) {
        oledLine2.textContent = line2;
        oledLine2.classList.add('fade-in');
        setTimeout(() => oledLine2.classList.remove('fade-in'), 300);
    }
}

// 初始化LED面板
function initializeLEDPanel() {
    // 初始化所有LED为关闭状态
    for (let i = 1; i <= 6; i++) {
        setLEDState(`led${i}`, false);
    }
}

// 设置LED状态
function setLEDState(ledId, isOn) {
    const led = document.getElementById(ledId);
    if (led) {
        if (isOn) {
            led.classList.add('on');
            led.classList.remove('blink');
        } else {
            led.classList.remove('on', 'blink');
        }
    }
}

// 开始LED闪烁
function startLEDBlink(ledId) {
    const led = document.getElementById(ledId);
    if (led) {
        led.classList.add('on', 'blink');
    }
}

// 停止所有LED闪烁
function stopAllLEDBlink() {
    for (let i = 1; i <= 6; i++) {
        const led = document.getElementById(`led${i}`);
        if (led) {
            led.classList.remove('on', 'blink');
        }
    }
}

// LED短暂闪烁
function flashLED(ledId, duration = 200) {
    const led = document.getElementById(ledId);
    if (led) {
        led.classList.add('on');
        setTimeout(() => {
            led.classList.remove('on');
        }, duration);
    }
}

// 初始化电压图表
function initializeVoltageChart() {
    const canvas = document.getElementById('voltageChart');
    if (!canvas || typeof Chart === 'undefined') {
        console.warn('Chart.js未加载或canvas元素不存在');
        return;
    }

    const ctx = canvas.getContext('2d');
    
    demoState.chart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: '电压值 (V)',
                data: [],
                borderColor: '#2563eb',
                backgroundColor: 'rgba(37, 99, 235, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4,
                pointRadius: 3,
                pointHoverRadius: 5
            }, {
                label: '超限阈值',
                data: [],
                borderColor: '#ef4444',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                borderWidth: 2,
                borderDash: [5, 5],
                fill: false,
                pointRadius: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    title: {
                        display: true,
                        text: '时间'
                    },
                    ticks: {
                        maxTicksLimit: 10
                    }
                },
                y: {
                    title: {
                        display: true,
                        text: '电压 (V)'
                    },
                    min: 0,
                    max: 4,
                    ticks: {
                        stepSize: 0.5
                    }
                }
            },
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': ' + context.parsed.y.toFixed(2) + 'V';
                        }
                    }
                }
            },
            interaction: {
                mode: 'nearest',
                axis: 'x',
                intersect: false
            },
            animation: {
                duration: 300
            }
        }
    });
}

// 更新电压图表
function updateVoltageChart() {
    if (!demoState.chart || demoState.voltageHistory.length === 0) return;

    const chart = demoState.chart;
    const history = demoState.voltageHistory;

    // 更新标签和数据
    chart.data.labels = history.map(item => 
        item.time.toLocaleTimeString('zh-CN', { 
            hour12: false, 
            hour: '2-digit', 
            minute: '2-digit', 
            second: '2-digit' 
        })
    );

    // 更新电压数据
    chart.data.datasets[0].data = history.map(item => item.voltage);

    // 更新阈值线
    chart.data.datasets[1].data = history.map(() => systemConfig.voltageLimit);

    // 更新图表
    chart.update('none'); // 无动画更新以提高性能
}

// 模拟系统状态变化
function simulateSystemActivity() {
    if (!demoState.isRunning) return;

    // 随机系统活动
    if (Math.random() < 0.1) {
        const activities = [
            () => flashLED('led3', 150),
            () => flashLED('led4', 150),
            () => flashLED('led5', 150),
            () => flashLED('led6', 150)
        ];

        const randomActivity = activities[Math.floor(Math.random() * activities.length)];
        randomActivity();
    }
}

// 重置演示系统
function resetDemo() {
    stopSampling();
    demoState.sampleCount = 0;
    demoState.voltageHistory = [];
    demoState.currentVoltage = 0.0;

    // 重置图表
    if (demoState.chart) {
        demoState.chart.data.labels = [];
        demoState.chart.data.datasets[0].data = [];
        demoState.chart.data.datasets[1].data = [];
        demoState.chart.update();
    }

    // 重置显示
    updateOLEDDisplay('system idle', '');
    initializeLEDPanel();

    console.log('演示系统已重置');
}

// 导出函数供外部调用
window.demoSystem = {
    start: startSampling,
    stop: stopSampling,
    reset: resetDemo,
    getState: () => ({ ...demoState }),
    setConfig: (config) => Object.assign(systemConfig, config)
};

// 定期模拟系统活动
setInterval(simulateSystemActivity, 1000);