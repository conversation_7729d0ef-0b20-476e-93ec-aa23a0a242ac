// 导航和页面交互功能JavaScript文件

// 导航状态管理
const navigationState = {
    currentSection: 'home',
    isMenuOpen: false,
    scrollPosition: 0,
    isScrolling: false,
    sections: ['home', 'architecture', 'modules', 'docs', 'demo']
};

// 页面内容数据
const pageContent = {
    architecture: {
        layers: [
            {
                name: '应用层',
                description: '系统启动、主循环控制、中断管理',
                components: ['main.c', 'systick.c', 'gd32f4xx_it.c'],
                color: '#ef4444'
            },
            {
                name: '功能层',
                description: '业务功能实现、任务调度、数据处理',
                components: ['scheduler.c', 'adc_app.c', 'sd_app.c', 'oled_app.c', 'btn_app.c', 'usart_app.c', 'rtc_app.c', 'led_app.c', 'error_handler.c'],
                color: '#2563eb'
            },
            {
                name: '驱动层',
                description: '硬件设备驱动、第三方组件集成',
                components: ['BSP', 'OLED', 'GD25QXX', 'EBTN', 'SDIO', 'FatFS'],
                color: '#f59e0b'
            },
            {
                name: '硬件抽象层',
                description: '底层硬件访问、寄存器操作、系统启动',
                components: ['GD32F4xx HAL', 'CMSIS', '启动文件'],
                color: '#10b981'
            }
        ]
    },
    modules: [
        {
            id: 'scheduler',
            name: '任务调度器',
            icon: 'fas fa-tasks',
            description: '时间片轮询调度，协调6个功能任务执行',
            stats: ['5ms 最小周期', '6个任务'],
            details: {
                features: ['预计算优化算法', '性能统计功能', '任务优先级管理', '实时调度保证'],
                performance: {
                    cpuUsage: '8%',
                    executionTime: '30μs',
                    taskCount: 6,
                    minPeriod: '5ms'
                }
            }
        },
        {
            id: 'adc',
            name: 'ADC采样模块',
            icon: 'fas fa-wave-square',
            description: '12位高精度电压采样，支持可配置采样周期',
            stats: ['12位精度', '0.8mV分辨率'],
            details: {
                features: ['DMA传输优化', '超限检测功能', '变比系数配置', '实时电压计算'],
                performance: {
                    resolution: '12位',
                    accuracy: '0.8mV',
                    sampleRate: '可配置1-3600s',
                    channels: 1
                }
            }
        },
        {
            id: 'storage',
            name: '存储管理',
            icon: 'fas fa-database',
            description: 'SD卡主存储+Flash备份，双重存储机制',
            stats: ['FAT32文件系统', '数据加密支持'],
            details: {
                features: ['自动备份机制', '文件完整性检查', '目录结构管理', '配置参数存储'],
                performance: {
                    sdCard: 'FAT32支持',
                    flash: '512KB容量',
                    backup: '双重存储',
                    encryption: '数据加密'
                }
            }
        },
        {
            id: 'display',
            name: '显示控制',
            icon: 'fas fa-tv',
            description: '0.91寸OLED实时显示电压值和系统状态',
            stats: ['128×32像素', '100ms更新'],
            details: {
                features: ['I2C通信接口', 'DMA传输优化', '多行文本显示', '实时状态更新'],
                performance: {
                    resolution: '128×32',
                    updateRate: '10Hz',
                    interface: 'I2C',
                    size: '0.91寸'
                }
            }
        },
        {
            id: 'input',
            name: '按键处理',
            icon: 'fas fa-hand-pointer',
            description: '7个按键输入，支持防抖和多种按键事件',
            stats: ['5ms扫描', '防抖处理'],
            details: {
                features: ['多按键支持', '防抖算法', '事件回调机制', '状态机管理'],
                performance: {
                    scanPeriod: '5ms',
                    debounce: '软件防抖',
                    buttons: 7,
                    events: '多种事件'
                }
            }
        },
        {
            id: 'comm',
            name: '串口通信',
            icon: 'fas fa-comments',
            description: '115200波特率调试输出和命令解析',
            stats: ['115200 bps', 'DMA传输'],
            details: {
                features: ['命令解析系统', 'DMA传输优化', '调试信息输出', '配置参数设置'],
                performance: {
                    baudRate: '115200',
                    transfer: 'DMA',
                    buffer: '512字节',
                    protocol: '文本协议'
                }
            }
        },
        {
            id: 'rtc',
            name: '时钟管理',
            icon: 'fas fa-clock',
            description: 'RTC实时时钟，精确时间戳记录',
            stats: ['实时时钟', '断电保持'],
            details: {
                features: ['时间戳记录', '上电计数管理', '时间格式转换', '备份寄存器'],
                performance: {
                    accuracy: '高精度',
                    backup: '断电保持',
                    format: '多种格式',
                    registers: '备份寄存器'
                }
            }
        },
        {
            id: 'led',
            name: 'LED指示',
            icon: 'fas fa-lightbulb',
            description: '6个LED状态指示，显示系统运行状态',
            stats: ['6个LED', '状态指示'],
            details: {
                features: ['状态指示功能', 'GPIO控制', '闪烁模式支持', '性能统计'],
                performance: {
                    count: 6,
                    control: 'GPIO',
                    modes: '多种模式',
                    period: '50ms更新'
                }
            }
        },
        {
            id: 'error',
            name: '错误处理',
            icon: 'fas fa-exclamation-triangle',
            description: '分级错误管理和自动恢复机制',
            stats: ['4级错误', '自动恢复'],
            details: {
                features: ['错误分级管理', '自动恢复机制', '错误日志记录', '系统保护功能'],
                performance: {
                    levels: 4,
                    recovery: '自动恢复',
                    logging: '错误日志',
                    protection: '系统保护'
                }
            }
        }
    ],
    documents: [
        {
            id: 'manual',
            title: '产品使用手册',
            icon: 'fas fa-book',
            description: '详细的产品介绍、硬件连接、软件配置和操作指南',
            pages: 482,
            type: '用户手册',
            sections: [
                '产品概述',
                '硬件连接指南',
                '软件配置',
                '操作指南',
                '维护指导',
                '安全注意事项',
                '技术支持'
            ]
        },
        {
            id: 'analysis',
            title: '工程任务分析',
            icon: 'fas fa-chart-bar',
            description: '项目背景、技术选型、功能需求和开发环境配置',
            pages: 455,
            type: '技术分析',
            sections: [
                '项目概述',
                '技术选型分析',
                '功能需求分析',
                '开发环境配置',
                '项目里程碑',
                '风险评估与应对'
            ]
        },
        {
            id: 'optimization',
            title: '工程系统优化',
            icon: 'fas fa-tachometer-alt',
            description: '性能优化、内存优化、功耗优化和实时性优化策略',
            pages: 607,
            type: '优化指南',
            sections: [
                '性能优化策略',
                '内存优化策略',
                '实时性优化',
                '代码优化策略',
                '资源利用分析',
                '性能测试与分析'
            ]
        },
        {
            id: 'debug',
            title: '系统功能调试',
            icon: 'fas fa-bug',
            description: '调试工具、测试策略、故障诊断和问题排查技术',
            pages: 873,
            type: '调试指南',
            sections: [
                '调试工具分析',
                '测试策略',
                '故障诊断',
                '问题排查流程',
                '测试用例设计',
                '性能监控'
            ]
        },
        {
            id: 'modules',
            title: '系统单元功能分析设计',
            icon: 'fas fa-cubes',
            description: '9个核心功能模块的详细分析和接口定义',
            pages: 1000,
            type: '设计文档',
            sections: [
                '模块关系图',
                '任务调度器模块',
                'ADC采样模块',
                '存储管理模块',
                '显示控制模块',
                '按键处理模块',
                '串口通信模块',
                '时钟管理模块',
                'LED指示模块',
                '错误处理模块'
            ]
        },
        {
            id: 'design',
            title: '综合系统设计',
            icon: 'fas fa-sitemap',
            description: '整体架构设计、数据流设计和资源管理',
            pages: 620,
            type: '架构设计',
            sections: [
                '系统整体架构',
                '系统初始化序列',
                '数据流设计',
                '控制流设计',
                '通信机制',
                '资源管理设计',
                '系统配置管理'
            ]
        }
    ]
};

// 初始化导航功能
function initializeNavigation() {
    setupSmoothScrolling();
    setupSectionObserver();
    setupMobileMenu();
    setupKeyboardNavigation();
    setupBreadcrumbs();
}

// 设置平滑滚动
function setupSmoothScrolling() {
    // 为所有内部链接添加平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const offsetTop = targetElement.offsetTop - 70;
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
                
                // 更新URL但不触发页面跳转
                history.pushState(null, null, `#${targetId}`);
                navigationState.currentSection = targetId;
            }
        });
    });
}

// 设置区域观察器
function setupSectionObserver() {
    const observerOptions = {
        root: null,
        rootMargin: '-70px 0px -50% 0px',
        threshold: 0
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const sectionId = entry.target.id;
                updateActiveNavigation(sectionId);
                navigationState.currentSection = sectionId;
                
                // 更新URL
                if (sectionId && sectionId !== 'home') {
                    history.replaceState(null, null, `#${sectionId}`);
                } else {
                    history.replaceState(null, null, window.location.pathname);
                }
            }
        });
    }, observerOptions);

    // 观察所有主要区域
    navigationState.sections.forEach(sectionId => {
        const section = document.getElementById(sectionId);
        if (section) {
            observer.observe(section);
        }
    });
}

// 更新活动导航状态
function updateActiveNavigation(sectionId) {
    // 更新导航链接状态
    document.querySelectorAll('.nav-link').forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === `#${sectionId}`) {
            link.classList.add('active');
        }
    });

    // 触发区域进入事件
    triggerSectionEnterAnimation(sectionId);
}

// 触发区域进入动画
function triggerSectionEnterAnimation(sectionId) {
    const section = document.getElementById(sectionId);
    if (!section) return;

    // 为区域内的元素添加动画
    const animatedElements = section.querySelectorAll('.overview-card, .module-card, .doc-card, .layer, .feature');
    
    animatedElements.forEach((element, index) => {
        // 移除之前的动画类
        element.classList.remove('fade-in-up', 'slide-in-left', 'slide-in-right');
        
        // 延迟添加动画
        setTimeout(() => {
            if (index % 2 === 0) {
                element.classList.add('fade-in-up');
            } else {
                element.classList.add('slide-in-left');
            }
        }, index * 100);
    });
}

// 设置移动端菜单
function setupMobileMenu() {
    const hamburger = document.querySelector('.hamburger');
    const navMenu = document.querySelector('.nav-menu');
    
    if (hamburger && navMenu) {
        hamburger.addEventListener('click', () => {
            navigationState.isMenuOpen = !navigationState.isMenuOpen;
            
            hamburger.classList.toggle('active');
            navMenu.classList.toggle('active');
            
            // 防止背景滚动
            if (navigationState.isMenuOpen) {
                document.body.style.overflow = 'hidden';
            } else {
                document.body.style.overflow = '';
            }
        });

        // 点击菜单项时关闭菜单
        navMenu.addEventListener('click', (e) => {
            if (e.target.classList.contains('nav-link')) {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
                navigationState.isMenuOpen = false;
                document.body.style.overflow = '';
            }
        });

        // 点击外部区域关闭菜单
        document.addEventListener('click', (e) => {
            if (navigationState.isMenuOpen && 
                !hamburger.contains(e.target) && 
                !navMenu.contains(e.target)) {
                hamburger.classList.remove('active');
                navMenu.classList.remove('active');
                navigationState.isMenuOpen = false;
                document.body.style.overflow = '';
            }
        });
    }
}

// 设置键盘导航
function setupKeyboardNavigation() {
    document.addEventListener('keydown', (e) => {
        // ESC键关闭模态框和菜单
        if (e.key === 'Escape') {
            // 关闭模态框
            const modal = document.querySelector('.modal-overlay');
            if (modal) {
                closeModal();
                return;
            }
            
            // 关闭移动端菜单
            if (navigationState.isMenuOpen) {
                const hamburger = document.querySelector('.hamburger');
                const navMenu = document.querySelector('.nav-menu');
                
                if (hamburger && navMenu) {
                    hamburger.classList.remove('active');
                    navMenu.classList.remove('active');
                    navigationState.isMenuOpen = false;
                    document.body.style.overflow = '';
                }
            }
        }
        
        // 方向键导航
        if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
            e.preventDefault();
            navigateToAdjacentSection(e.key === 'ArrowUp' ? -1 : 1);
        }
    });
}

// 导航到相邻区域
function navigateToAdjacentSection(direction) {
    const currentIndex = navigationState.sections.indexOf(navigationState.currentSection);
    const newIndex = currentIndex + direction;
    
    if (newIndex >= 0 && newIndex < navigationState.sections.length) {
        const targetSection = navigationState.sections[newIndex];
        scrollToSection(targetSection);
    }
}

// 设置面包屑导航
function setupBreadcrumbs() {
    // 创建面包屑容器
    const breadcrumbContainer = document.createElement('div');
    breadcrumbContainer.className = 'breadcrumb-container';
    breadcrumbContainer.innerHTML = `
        <div class="container">
            <nav class="breadcrumb">
                <span class="breadcrumb-item active" data-section="home">
                    <i class="fas fa-home"></i>
                    首页
                </span>
            </nav>
        </div>
    `;
    
    // 插入到导航栏下方
    const navbar = document.querySelector('.navbar');
    if (navbar) {
        navbar.after(breadcrumbContainer);
    }
    
    // 监听区域变化更新面包屑
    document.addEventListener('sectionChange', (e) => {
        updateBreadcrumb(e.detail.sectionId);
    });
}

// 更新面包屑
function updateBreadcrumb(sectionId) {
    const breadcrumb = document.querySelector('.breadcrumb');
    if (!breadcrumb) return;
    
    const sectionNames = {
        home: '首页',
        architecture: '系统架构',
        modules: '功能模块',
        docs: '技术文档',
        demo: '系统演示'
    };
    
    const sectionIcons = {
        home: 'fas fa-home',
        architecture: 'fas fa-layer-group',
        modules: 'fas fa-cubes',
        docs: 'fas fa-book',
        demo: 'fas fa-play'
    };
    
    breadcrumb.innerHTML = `
        <span class="breadcrumb-item ${sectionId === 'home' ? 'active' : ''}" data-section="home">
            <i class="fas fa-home"></i>
            首页
        </span>
        ${sectionId !== 'home' ? `
            <span class="breadcrumb-separator">
                <i class="fas fa-chevron-right"></i>
            </span>
            <span class="breadcrumb-item active" data-section="${sectionId}">
                <i class="${sectionIcons[sectionId] || 'fas fa-file'}"></i>
                ${sectionNames[sectionId] || sectionId}
            </span>
        ` : ''}
    `;
    
    // 添加点击事件
    breadcrumb.querySelectorAll('.breadcrumb-item').forEach(item => {
        item.addEventListener('click', () => {
            const targetSection = item.getAttribute('data-section');
            scrollToSection(targetSection);
        });
    });
}

// 获取当前区域信息
function getCurrentSectionInfo() {
    const sectionId = navigationState.currentSection;
    
    switch (sectionId) {
        case 'architecture':
            return pageContent.architecture;
        case 'modules':
            return pageContent.modules;
        case 'docs':
            return pageContent.documents;
        default:
            return null;
    }
}

// 搜索功能
function setupSearch() {
    // 创建搜索框
    const searchContainer = document.createElement('div');
    searchContainer.className = 'search-container';
    searchContainer.innerHTML = `
        <div class="search-box">
            <input type="text" placeholder="搜索模块、文档或功能..." class="search-input">
            <button class="search-button">
                <i class="fas fa-search"></i>
            </button>
        </div>
        <div class="search-results"></div>
    `;
    
    // 添加到导航栏
    const navContainer = document.querySelector('.nav-container');
    if (navContainer) {
        navContainer.appendChild(searchContainer);
    }
    
    // 搜索功能实现
    const searchInput = searchContainer.querySelector('.search-input');
    const searchResults = searchContainer.querySelector('.search-results');
    
    searchInput.addEventListener('input', debounce((e) => {
        const query = e.target.value.trim();
        if (query.length > 2) {
            performSearch(query, searchResults);
        } else {
            searchResults.innerHTML = '';
            searchResults.style.display = 'none';
        }
    }, 300));
}

// 执行搜索
function performSearch(query, resultsContainer) {
    const results = [];
    
    // 搜索模块
    pageContent.modules.forEach(module => {
        if (module.name.includes(query) || module.description.includes(query)) {
            results.push({
                type: 'module',
                title: module.name,
                description: module.description,
                section: 'modules',
                id: module.id
            });
        }
    });
    
    // 搜索文档
    pageContent.documents.forEach(doc => {
        if (doc.title.includes(query) || doc.description.includes(query)) {
            results.push({
                type: 'document',
                title: doc.title,
                description: doc.description,
                section: 'docs',
                id: doc.id
            });
        }
    });
    
    // 显示搜索结果
    displaySearchResults(results, resultsContainer);
}

// 显示搜索结果
function displaySearchResults(results, container) {
    if (results.length === 0) {
        container.innerHTML = '<div class="search-no-results">未找到相关内容</div>';
    } else {
        container.innerHTML = results.map(result => `
            <div class="search-result-item" onclick="navigateToSearchResult('${result.section}', '${result.id}')">
                <div class="search-result-title">${result.title}</div>
                <div class="search-result-description">${result.description}</div>
                <div class="search-result-type">${result.type === 'module' ? '功能模块' : '技术文档'}</div>
            </div>
        `).join('');
    }
    
    container.style.display = 'block';
}

// 导航到搜索结果
function navigateToSearchResult(section, id) {
    // 先导航到对应区域
    scrollToSection(section);
    
    // 延迟高亮对应元素
    setTimeout(() => {
        const targetElement = document.querySelector(`[data-${section.slice(0, -1)}="${id}"]`);
        if (targetElement) {
            targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            targetElement.classList.add('highlight');
            setTimeout(() => targetElement.classList.remove('highlight'), 2000);
        }
    }, 500);
    
    // 隐藏搜索结果
    const searchResults = document.querySelector('.search-results');
    if (searchResults) {
        searchResults.style.display = 'none';
    }
}

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 导出导航功能
window.navigation = {
    scrollToSection,
    getCurrentSection: () => navigationState.currentSection,
    getSectionInfo: getCurrentSectionInfo,
    navigateToAdjacentSection,
    updateActiveNavigation
};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', initializeNavigation);