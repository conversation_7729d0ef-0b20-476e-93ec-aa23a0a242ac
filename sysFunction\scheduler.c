
#include "my_config.h"

/// 全局变量，用于存储任务数量
uint8_t task_num;

/// 性能统计变量：调度器运行次数统计
static uint32_t scheduler_run_count = 0;

typedef struct {
    void (*task_func)(void);    ///< 任务函数指针
    uint32_t rate_ms;          ///< 任务执行周期（毫秒）
    uint32_t last_run;         ///< 上次运行时间（毫秒）
    uint32_t next_run;         ///< 预计算的下次运行时间（毫秒）
} task_t;

// 静态任务数组，每个任务包含：任务函数、执行周期（毫秒）、上次运行时间（毫秒）、下次运行时间（毫秒）
static task_t scheduler_task[] =
{
     {adc_task,  TASK_PERIOD_ADC_MS,   0,   TASK_PERIOD_ADC_MS}   // ADC任务，100ms周期
    ,{led_task,  TASK_PERIOD_LED_MS,   0,   TASK_PERIOD_LED_MS}   // LED任务，50ms周期调用
    ,{oled_task, TASK_PERIOD_OLED_MS,  0,   TASK_PERIOD_OLED_MS}  // OLED任务，100ms周期调用（与ADC采样同步）
    ,{btn_task,  TASK_PERIOD_BTN_MS,   0,   TASK_PERIOD_BTN_MS}   // 按键任务，5ms周期（高优先级）
    ,{uart_task, TASK_PERIOD_UART_MS,  0,   TASK_PERIOD_UART_MS}  // 串口任务，5ms周期（高优先级）
    ,{rtc_task,  TASK_PERIOD_RTC_MS,   0,   TASK_PERIOD_RTC_MS}   // RTC任务，500ms周期

};

void scheduler_init(void)
{
    // 计算任务数组元素个数，将任务数量存储到 task_num 中
    task_num = sizeof(scheduler_task) / sizeof(task_t);
}

void scheduler_run(void)
{
    // 性能统计：记录调度器运行次数
    scheduler_run_count++;

    // 性能优化：只在函数开始时获取一次系统时间
    uint32_t current_time = get_system_ms();

    // 遍历任务数组中的所有任务
    for (uint8_t i = 0; i < task_num; i++)
    {
        // 优化的时间比较：使用预计算的下次运行时间
        if (current_time >= scheduler_task[i].next_run)
        {
            // 更新任务的上次运行时间
            scheduler_task[i].last_run = current_time;

            // 预计算下次运行时间，避免重复计算
            scheduler_task[i].next_run = current_time + scheduler_task[i].rate_ms;

            // 执行任务函数
            scheduler_task[i].task_func();
        }
    }
}

uint32_t scheduler_get_run_count(void)
{
    return scheduler_run_count;
}

void scheduler_reset_stats(void)
{
    scheduler_run_count = 0;
}
