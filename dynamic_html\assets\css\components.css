/* 系统架构部分样式 */
.architecture-section {
    background: var(--bg-primary);
}

.architecture-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
}

.architecture-diagram {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.layer {
    background: var(--bg-card);
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--primary-color);
    transition: all 0.3s ease;
}

.layer:hover {
    transform: translateX(5px);
    box-shadow: var(--shadow-lg);
}

.layer h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.layer-components {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.component {
    background: var(--bg-secondary);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.component:hover {
    background: var(--primary-color);
    color: var(--text-white);
    transform: scale(1.05);
}

.application-layer {
    border-left-color: #ef4444;
}

.function-layer {
    border-left-color: var(--primary-color);
}

.driver-layer {
    border-left-color: var(--accent-color);
}

.hal-layer {
    border-left-color: var(--success-color);
}

.architecture-description {
    background: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.architecture-description h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.feature-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.feature {
    display: flex;
    gap: var(--spacing-md);
    align-items: flex-start;
}

.feature i {
    font-size: 1.5rem;
    color: var(--primary-color);
    margin-top: var(--spacing-xs);
    flex-shrink: 0;
}

.feature h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
    color: var(--text-primary);
}

.feature p {
    color: var(--text-secondary);
    line-height: 1.5;
}

/* 功能模块部分样式 */
.modules-section {
    background: var(--bg-secondary);
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.module-card {
    background: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.module-card:hover::before {
    transform: scaleX(1);
}

.module-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.module-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    transition: all 0.3s ease;
}

.module-card:hover .module-icon {
    transform: scale(1.1) rotate(5deg);
}

.module-icon i {
    font-size: 1.5rem;
    color: var(--text-white);
}

.module-card h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.module-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.module-stats {
    display: flex;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

.stat {
    background: var(--bg-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

/* 技术文档部分样式 */
.docs-section {
    background: var(--bg-primary);
}

.docs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);
}

.doc-card {
    background: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.doc-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 20px 20px 0;
    border-color: transparent var(--primary-color) transparent transparent;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.doc-card:hover::after {
    opacity: 1;
}

.doc-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
    border-color: var(--primary-color);
}

.doc-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--accent-color), #f59e0b);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
    transition: all 0.3s ease;
}

.doc-card:hover .doc-icon {
    transform: scale(1.1);
    background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
}

.doc-icon i {
    font-size: 1.5rem;
    color: var(--text-white);
}

.doc-card h3 {
    font-size: 1.3rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.doc-card p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
    line-height: 1.6;
}

.doc-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.doc-pages,
.doc-type {
    background: var(--bg-secondary);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius);
    font-size: 0.8rem;
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

.doc-type {
    background: var(--primary-color);
    color: var(--text-white);
    border-color: var(--primary-color);
}

/* 系统演示部分样式 */
.demo-section {
    background: var(--bg-secondary);
}

.demo-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: start;
}

.demo-controls {
    background: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.demo-controls h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

.control-group {
    margin-bottom: var(--spacing-lg);
}

.control-group label {
    display: block;
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--text-primary);
}

.control-group input[type="range"] {
    width: 100%;
    margin: var(--spacing-sm) 0;
    -webkit-appearance: none;
    appearance: none;
    height: 6px;
    background: var(--bg-secondary);
    border-radius: 3px;
    outline: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
}

.control-group input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.demo-display {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.virtual-oled {
    background: #000;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 2px solid #333;
}

.oled-screen {
    background: #001122;
    padding: var(--spacing-md);
    border-radius: 4px;
    font-family: var(--font-mono);
    color: #00ff00;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.oled-line {
    font-size: 0.9rem;
    line-height: 1.4;
    margin: 2px 0;
}

.led-panel {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-md);
    background: var(--bg-card);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

.led {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #333;
    position: relative;
    transition: all 0.3s ease;
    cursor: pointer;
}

.led::after {
    content: attr(data-label);
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.7rem;
    color: var(--text-secondary);
    white-space: nowrap;
    margin-top: var(--spacing-xs);
}

.led.on {
    background: radial-gradient(circle, #ff4444, #cc0000);
    box-shadow: 0 0 20px rgba(255, 68, 68, 0.6);
}

.led.blink {
    animation: ledBlink 1s infinite;
}

.demo-chart {
    grid-column: 1 / -1;
    background: var(--bg-card);
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-color);
}

/* 页脚样式 */
.footer {
    background: var(--bg-dark);
    color: var(--text-white);
    padding: var(--spacing-2xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.footer-section h3 {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-white);
}

.footer-section p,
.footer-section li {
    color: #94a3b8;
    line-height: 1.6;
    margin-bottom: var(--spacing-xs);
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    padding: var(--spacing-xs) 0;
}

.footer-bottom {
    border-top: 1px solid #374151;
    padding-top: var(--spacing-lg);
    text-align: center;
    color: #6b7280;
}

/* 动画效果 */
@keyframes ledBlink {
    0%, 50% {
        opacity: 1;
    }
    51%, 100% {
        opacity: 0.3;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .architecture-content {
        grid-template-columns: 1fr;
    }

    .architecture-description {
        position: static;
    }

    .demo-content {
        grid-template-columns: 1fr;
    }

    .modules-grid {
        grid-template-columns: 1fr;
    }

    .docs-grid {
        grid-template-columns: 1fr;
    }

    .led-panel {
        grid-template-columns: repeat(2, 1fr);
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .layer-components {
        flex-direction: column;
    }

    .component {
        text-align: center;
    }

    .module-stats {
        justify-content: center;
    }

    .doc-meta {
        flex-direction: column;
        gap: var(--spacing-sm);
        align-items: center;
    }

    .led-panel {
        grid-template-columns: 1fr;
    }

    .control-group {
        text-align: center;
    }
}