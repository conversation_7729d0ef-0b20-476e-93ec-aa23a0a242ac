/* 全局样式重置和基础设置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主题色彩 */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-dark: #1e293b;
    --bg-card: #ffffff;
    
    /* 文字色彩 */
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --text-light: #94a3b8;
    --text-white: #ffffff;
    
    /* 边框和阴影 */
    --border-color: #e2e8f0;
    --border-radius: 8px;
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);
    
    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* 字体 */
    --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    --font-mono: 'Consolas', 'Monaco', 'Courier New', monospace;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

/* 容器和布局 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.section {
    padding: var(--spacing-2xl) 0;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    color: var(--text-primary);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
    border-radius: 2px;
}

/* 导航栏样式 */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-logo i {
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-xl);
}

.nav-link {
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background-color: rgba(37, 99, 235, 0.1);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--text-primary);
    transition: 0.3s;
    border-radius: 2px;
}

/* 主要内容区域 */
.main-content {
    margin-top: 70px;
}

/* 英雄区域样式 */
.hero-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--text-white);
    min-height: 90vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.3;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-2xl);
    align-items: center;
    position: relative;
    z-index: 1;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    line-height: 1.2;
    margin-bottom: var(--spacing-lg);
}

.highlight {
    background: linear-gradient(45deg, #fbbf24, #f59e0b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
}

.hero-features {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
}

.feature-item i {
    color: var(--accent-color);
    font-size: 1.2rem;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-md);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-white);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: transparent;
    color: var(--text-white);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-success {
    background: var(--success-color);
    color: var(--text-white);
}

.btn-success:hover {
    background: #059669;
}

.btn-danger {
    background: var(--error-color);
    color: var(--text-white);
}

.btn-danger:hover {
    background: #dc2626;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

/* 英雄区域视觉效果 */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.system-diagram {
    position: relative;
    width: 300px;
    height: 300px;
}

.mcu-chip {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 120px;
    height: 120px;
    background: linear-gradient(45deg, #1e293b, #334155);
    border-radius: 15px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    box-shadow: var(--shadow-xl);
    border: 2px solid var(--accent-color);
}

.mcu-chip i {
    font-size: 2rem;
    margin-bottom: var(--spacing-sm);
    color: var(--accent-color);
}

.mcu-chip span {
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
}

.peripheral-connections {
    position: relative;
    width: 100%;
    height: 100%;
}

.peripheral {
    position: absolute;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: var(--text-white);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    animation: float 3s ease-in-out infinite;
}

.peripheral.adc {
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0s;
}

.peripheral.storage {
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    animation-delay: 0.5s;
}

.peripheral.display {
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 1s;
}

.peripheral.comm {
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    animation-delay: 1.5s;
}

.peripheral i {
    font-size: 1.5rem;
    margin-bottom: var(--spacing-xs);
    color: var(--accent-color);
}

.peripheral span {
    font-size: 0.7rem;
    font-weight: 600;
}

/* 概览部分样式 */
.overview-section {
    background: var(--bg-secondary);
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
}

.overview-card {
    background: var(--bg-card);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    border: 1px solid var(--border-color);
}

.overview-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

.card-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-lg);
}

.card-icon i {
    font-size: 1.5rem;
    color: var(--text-white);
}

.overview-card h3 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
}

.overview-card ul {
    list-style: none;
}

.overview-card li {
    padding: var(--spacing-xs) 0;
    color: var(--text-secondary);
    position: relative;
    padding-left: var(--spacing-lg);
}

.overview-card li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

/* 动画效果 */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--bg-card);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: var(--shadow-lg);
        padding: var(--spacing-lg) 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active .bar:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active .bar:nth-child(1) {
        transform: translateY(7px) rotate(45deg);
    }

    .hamburger.active .bar:nth-child(3) {
        transform: translateY(-7px) rotate(-45deg);
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-features {
        grid-template-columns: 1fr;
    }

    .hero-buttons {
        justify-content: center;
        flex-wrap: wrap;
    }

    .system-diagram {
        width: 250px;
        height: 250px;
    }

    .mcu-chip {
        width: 100px;
        height: 100px;
    }

    .peripheral {
        width: 60px;
        height: 60px;
    }

    .peripheral i {
        font-size: 1.2rem;
    }

    .peripheral span {
        font-size: 0.6rem;
    }

    .overview-grid {
        grid-template-columns: 1fr;
    }

    .section-title {
        font-size: 2rem;
    }

    .container {
        padding: 0 var(--spacing-md);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: 0.9rem;
    }

    .system-diagram {
        width: 200px;
        height: 200px;
    }

    .mcu-chip {
        width: 80px;
        height: 80px;
    }

    .mcu-chip i {
        font-size: 1.5rem;
    }

    .mcu-chip span {
        font-size: 0.7rem;
    }

    .peripheral {
        width: 50px;
        height: 50px;
    }

    .peripheral i {
        font-size: 1rem;
    }

    .peripheral span {
        font-size: 0.5rem;
    }
}